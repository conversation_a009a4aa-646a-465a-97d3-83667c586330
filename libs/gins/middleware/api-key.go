package middleware

import (
	"net/http"
	"strings"

	"digital-transformation-api/infrastructure"
	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/errs"

	"github.com/gin-gonic/gin"
)

// Api<PERSON>ey middleware validates API key from headers, query parameters, or request body
func ApiKey() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		expectedApiKey := infrastructure.GetApiKey()
		if expectedApiKey == "" {
			// If no API key is configured, skip validation
			ctx.Next()
			return
		}

		var providedApiKey string

		// 1. Check for API key in headers (primary method)
		providedApiKey = ctx.GetHeader(apps.ApiKey)
		if providedApiKey == "" {
			// Also check common alternative header names
			providedApiKey = ctx.GetHeader("Authorization")
			if providedApiKey != "" {
				// Handle "Bearer <token>" format
				if strings.HasPrefix(providedApiKey, "Bearer ") {
					providedApiKey = strings.TrimPrefix(provided<PERSON><PERSON><PERSON><PERSON>, "<PERSON><PERSON> ")
				}
			}
		}

		// 2. Check for API key in query parameters (fallback)
		if providedApiKey == "" {
			providedApiKey = ctx.Query("api_key")
			if providedApiKey == "" {
				providedApiKey = ctx.Query("apikey")
			}
		}

		// 3. Check for API key in request body (for POST/PUT requests)
		if providedApiKey == "" && (ctx.Request.Method == http.MethodPost || ctx.Request.Method == http.MethodPut || ctx.Request.Method == http.MethodPatch) {
			// Try to get from form data
			providedApiKey = ctx.PostForm("api_key")
			if providedApiKey == "" {
				providedApiKey = ctx.PostForm("apikey")
			}
		}

		// Validate API key presence
		if providedApiKey == "" {
			err := errs.NewCustom(
				http.StatusUnauthorized,
				errs.Err40100,
				"API key is required",
				"API key must be provided in x-api-key header, Authorization header, or as a query parameter",
			)
			ctx.AbortWithStatusJSON(err.Status(), err)
			return
		}

		// Validate API key value
		if providedApiKey != expectedApiKey {
			err := errs.NewCustom(
				http.StatusUnauthorized,
				errs.Err40101,
				"Invalid API key",
				"The provided API key is not valid",
			)
			ctx.AbortWithStatusJSON(err.Status(), err)
			return
		}

		// API key is valid, continue to next middleware/handler
		ctx.Next()
	}
}
